// Dashboard JavaScript
class Dashboard {
    constructor() {
        this.statusUpdateInterval = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadPosts();
        this.startStatusUpdates();
        this.initRealTimeUpdates();
        this.startHeartbeatMonitoring();
    }

    bindEvents() {
        // Monitor controls
        document.getElementById('start-monitor').addEventListener('click', () => this.startMonitor());
        document.getElementById('stop-monitor').addEventListener('click', () => this.stopMonitor());
        
        // Test detection
        document.getElementById('test-detection').addEventListener('click', () => this.testDetection());
        
        // Refresh posts
        document.getElementById('refresh-posts').addEventListener('click', () => this.loadPosts());

        // Toggle performance monitor
        document.getElementById('toggle-performance').addEventListener('click', () => this.togglePerformanceMonitor());
        
        // Enter key for test detection
        document.getElementById('test-text').addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                this.testDetection();
            }
        });
    }

    async startMonitor() {
        try {
            this.showLoading('start-monitor');
            const response = await fetch('/api/monitor/start', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
                this.showToast('Auto monitor started successfully!', 'success');
                this.updateMonitorButtons(true);
            } else {
                this.showToast(result.error || 'Failed to start monitor', 'error');
            }
        } catch (error) {
            this.showToast('Error starting monitor: ' + error.message, 'error');
        } finally {
            this.hideLoading('start-monitor');
        }
    }

    async stopMonitor() {
        try {
            this.showLoading('stop-monitor');
            const response = await fetch('/api/monitor/stop', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
                this.showToast('Auto monitor stopped successfully!', 'success');
                this.updateMonitorButtons(false);
            } else {
                this.showToast(result.error || 'Failed to stop monitor', 'error');
            }
        } catch (error) {
            this.showToast('Error stopping monitor: ' + error.message, 'error');
        } finally {
            this.hideLoading('stop-monitor');
        }
    }

    async testDetection() {
        const textArea = document.getElementById('test-text');
        const text = textArea.value.trim();
        
        if (!text) {
            this.showToast('Please enter text to test', 'warning');
            return;
        }

        try {
            this.showLoading('test-detection');
            const response = await fetch('/api/test-detection', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ text })
            });
            
            const result = await response.json();
            this.displayTestResult(result);
        } catch (error) {
            this.showToast('Error testing detection: ' + error.message, 'error');
        } finally {
            this.hideLoading('test-detection');
        }
    }

    displayTestResult(result) {
        const container = document.getElementById('test-result');
        const isSpam = result.is_spam;
        const confidence = (result.confidence * 100).toFixed(1);
        
        container.innerHTML = `
            <div class="test-result ${isSpam ? 'spam' : 'normal'} fade-in">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="badge bg-${isSpam ? 'danger' : 'success'} prediction-badge">
                        ${result.label.toUpperCase()}
                    </span>
                    <small class="text-muted">Confidence: ${confidence}%</small>
                </div>
                <div class="confidence-bar">
                    <div class="confidence-fill ${this.getConfidenceClass(result.confidence)}" 
                         style="width: ${confidence}%"></div>
                </div>
                ${result.error ? `<div class="text-danger mt-2"><small>Error: ${result.error}</small></div>` : ''}
            </div>
        `;
    }

    async loadPosts() {
        try {
            this.showLoading('refresh-posts');
            console.log('📡 Loading posts with comments...');
            const startTime = Date.now();

            // Use the new endpoint that loads posts with comments
            const response = await fetch('/api/posts-with-comments');
            const posts = await response.json();

            if (response.ok) {
                const loadTime = Date.now() - startTime;
                console.log(`⚡ Posts with comments loaded in ${loadTime}ms`);
                await this.displayPostsWithComments(posts);
            } else {
                this.showToast(posts.error || 'Failed to load posts', 'error');
                document.getElementById('posts-container').innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        ${posts.error || 'Failed to load posts'}
                    </div>
                `;
            }
        } catch (error) {
            console.error('❌ Error loading posts:', error);
            this.showToast('Error loading posts: ' + error.message, 'error');
        } finally {
            this.hideLoading('refresh-posts');
        }
    }

    async displayPosts(posts) {
        const container = document.getElementById('posts-container');

        if (posts.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>No posts found</p>
                </div>
            `;
            return;
        }

        let html = '';
        for (const post of posts) {
            html += this.renderPost(post);
        }

        container.innerHTML = html;

        // Bind click events to post headers
        this.bindPostClickEvents();
    }

    async displayPostsWithComments(posts) {
        const container = document.getElementById('posts-container');

        if (posts.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>No posts found</p>
                </div>
            `;
            return;
        }

        let html = '';
        for (const post of posts) {
            html += this.renderPostWithComments(post);
        }

        container.innerHTML = html;

        // Bind click events to post headers for collapsible functionality
        this.bindPostClickEvents();

        // Show success message with stats
        const totalComments = posts.reduce((sum, post) => sum + (post.comments?.length || 0), 0);
        const postsWithComments = posts.filter(post => post.comments && post.comments.length > 0).length;

        if (totalComments > 0) {
            this.showToast(`Loaded ${posts.length} posts with ${totalComments} comments (${postsWithComments} posts have comments)`, 'success');
        } else {
            this.showToast(`Loaded ${posts.length} posts (no comments found)`, 'info');
        }
    }

    async loadCommentsForPost(postId, retries = 2) {
        const startTime = Date.now();
        let controller = null;
        let timeoutId = null;

        try {
            console.log(`📡 Fetching comments for post ${postId}...`);

            controller = new AbortController();

            // Increase timeout for AI processing
            const timeoutMs = 30000; // 30 seconds for AI processing
            timeoutId = setTimeout(() => {
                console.log(`⏰ Request timeout after ${timeoutMs}ms`);
                controller.abort();
            }, timeoutMs);

            const response = await fetch(`/api/posts/${postId}/comments`, {
                signal: controller.signal,
                headers: {
                    'Cache-Control': 'no-cache'
                }
            });

            if (timeoutId) {
                clearTimeout(timeoutId);
                timeoutId = null;
            }

            if (response.ok) {
                const comments = await response.json();
                const totalTime = Date.now() - startTime;

                console.log(`✅ Received ${comments.length} comments in ${totalTime}ms`);

                // Extract performance metrics from response headers
                const cacheStatus = response.headers.get('X-Cache-Status') || 'unknown';
                const fbTime = response.headers.get('X-FB-Time');
                const aiTime = response.headers.get('X-AI-Time');
                const serverTotalTime = response.headers.get('X-Total-Time');

                // Update performance monitor
                this.updatePerformanceMonitor({
                    totalTime: totalTime,
                    cached: cacheStatus === 'HIT',
                    apiTime: fbTime ? parseInt(fbTime) : null,
                    aiTime: aiTime ? parseInt(aiTime) : null,
                    serverTime: serverTotalTime ? parseInt(serverTotalTime) : null
                });

                return comments;
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            // Clean up timeout
            if (timeoutId) {
                clearTimeout(timeoutId);
            }

            const totalTime = Date.now() - startTime;

            // Handle different error types
            if (error.name === 'AbortError') {
                console.warn(`⏰ Request aborted after ${totalTime}ms (timeout or user action)`);
                if (retries > 0) {
                    console.log(`🔄 Retrying due to timeout... (${retries} attempts left)`);
                    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
                    return this.loadCommentsForPost(postId, retries - 1);
                } else {
                    throw new Error('Request timeout - AI processing is taking too long. Please try again.');
                }
            } else {
                console.error(`❌ Error loading comments (attempt ${3 - retries}) after ${totalTime}ms:`, error.message);

                if (retries > 0) {
                    console.log(`🔄 Retrying... (${retries} attempts left)`);
                    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
                    return this.loadCommentsForPost(postId, retries - 1);
                }
            }

            throw error;
        }
    }

    renderPost(post) {
        const postDate = new Date(post.created_time).toLocaleString();
        const postIdShort = post.id.split('_')[1];

        return `
            <div class="post-item fade-in" data-post-id="${post.id}">
                <div class="post-header" onclick="dashboard.toggleComments('${post.id}')">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-file-text me-2 text-primary"></i>
                                <h6 class="mb-0">Post ${postIdShort}</h6>
                                <i class="fas fa-chevron-down ms-2 expand-icon" id="icon-${post.id}"></i>
                            </div>
                            <p class="text-muted mb-2">${post.message ? post.message.substring(0, 150) + (post.message.length > 150 ? '...' : '') : 'No message'}</p>
                            <div class="d-flex align-items-center">
                                <small class="text-muted me-3">
                                    <i class="fas fa-calendar me-1"></i>
                                    ${postDate}
                                </small>
                                <span class="badge bg-info me-2" id="comment-count-${post.id}">
                                    <i class="fas fa-comments me-1"></i>
                                    Loading...
                                </span>
                                <span class="badge bg-warning" id="spam-count-${post.id}" style="display: none;">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    Spam detected
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="comments-section" id="comments-${post.id}">
                    <div class="comments-header">
                        <i class="fas fa-comments me-2"></i>
                        Comments
                        <span class="float-end">
                            <button class="btn btn-sm btn-outline-primary" onclick="dashboard.refreshComments('${post.id}')">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </span>
                    </div>
                    <div class="comments-container" id="comments-container-${post.id}">
                        <div class="text-center p-3">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 mb-0">Loading comments...</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderPostWithComments(post) {
        const postDate = new Date(post.created_time).toLocaleString();
        const postIdShort = post.id.split('_')[1];
        const comments = post.comments || [];
        const commentsLoaded = post.commentsLoaded || false;
        const hasError = post.error;

        // Calculate stats
        const totalComments = comments.length;
        const spamComments = comments.filter(c => c.prediction?.is_spam).length;
        const normalComments = totalComments - spamComments;

        return `
            <div class="post-item fade-in ${commentsLoaded ? 'comments-loaded' : ''}" data-post-id="${post.id}">
                <div class="post-header" onclick="dashboard.toggleComments('${post.id}')">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-file-text me-2 text-primary"></i>
                                <h6 class="mb-0">Post ${postIdShort}</h6>
                                ${post.cacheHit ? '<span class="badge bg-info ms-2" title="Loaded from cache"><i class="fas fa-bolt"></i></span>' : ''}
                                <i class="fas fa-chevron-down ms-2 expand-icon" id="icon-${post.id}"></i>
                            </div>
                            <p class="text-muted mb-2">${post.message ? post.message.substring(0, 150) + (post.message.length > 150 ? '...' : '') : 'No message'}</p>
                            <div class="d-flex align-items-center">
                                <small class="text-muted me-3">
                                    <i class="fas fa-calendar me-1"></i>
                                    ${postDate}
                                </small>
                                <span class="badge bg-${totalComments > 0 ? 'primary' : 'secondary'} me-2" id="comment-count-${post.id}">
                                    <i class="fas fa-comments me-1"></i>
                                    ${totalComments} Comments
                                </span>
                                <span class="badge bg-${spamComments > 0 ? 'danger' : 'success'} me-2" id="spam-count-${post.id}" ${spamComments === 0 ? 'style="display: none;"' : ''}>
                                    <i class="fas fa-${spamComments > 0 ? 'exclamation-triangle' : 'shield-alt'} me-1"></i>
                                    ${spamComments} Spam
                                </span>
                                ${commentsLoaded && !hasError ? `
                                    <span class="badge bg-success me-2">
                                        <i class="fas fa-check me-1"></i>Ready
                                    </span>
                                ` : hasError ? `
                                    <span class="badge bg-warning me-2">
                                        <i class="fas fa-exclamation-triangle me-1"></i>Error
                                    </span>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="comments-section ${commentsLoaded ? 'preloaded' : ''}" id="comments-${post.id}">
                    <div class="comments-header">
                        <i class="fas fa-comments me-2"></i>
                        Comments
                        ${commentsLoaded && !hasError ? `
                            <span class="badge bg-success ms-2">
                                <i class="fas fa-check me-1"></i>Loaded
                            </span>
                        ` : hasError ? `
                            <span class="badge bg-warning ms-2">
                                <i class="fas fa-exclamation-triangle me-1"></i>Error
                            </span>
                        ` : ''}
                        <span class="float-end">
                            <button class="btn btn-sm btn-outline-primary" onclick="dashboard.refreshComments('${post.id}')">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </span>
                    </div>
                    <div class="comments-container" id="comments-container-${post.id}">
                        ${this.renderCommentsContent(post)}
                    </div>
                </div>
            </div>
        `;
    }

    renderCommentsContent(post) {
        const comments = post.comments || [];
        const commentsLoaded = post.commentsLoaded || false;
        const hasError = post.error;

        if (hasError) {
            return `
                <div class="alert alert-warning m-3">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Loading Failed</strong>
                    </div>
                    <p class="mb-2">${hasError}</p>
                    <button class="btn btn-sm btn-outline-primary" onclick="dashboard.loadAndDisplayComments('${post.id}')">
                        <i class="fas fa-redo me-1"></i>Retry
                    </button>
                </div>
            `;
        }

        if (!commentsLoaded) {
            return `
                <div class="text-center p-3">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 mb-0">Loading comments...</p>
                </div>
            `;
        }

        if (comments.length === 0) {
            return `
                <div class="text-center text-muted p-3">
                    <i class="fas fa-comment-slash fa-2x mb-2"></i>
                    <p class="mb-0">No comments found</p>
                </div>
            `;
        }

        return comments.map(comment => this.renderComment(comment)).join('');
    }

    bindPostClickEvents() {
        // Event listeners sudah di-handle oleh onclick di HTML
        // Method ini untuk future enhancements
    }

    async toggleComments(postId) {
        const commentsSection = document.getElementById(`comments-${postId}`);
        const expandIcon = document.getElementById(`icon-${postId}`);
        const postItem = document.querySelector(`[data-post-id="${postId}"]`);

        if (commentsSection.classList.contains('show')) {
            // Hide comments
            commentsSection.classList.remove('show');
            expandIcon.classList.remove('expanded');
            postItem.classList.remove('expanded');
        } else {
            // Show comments
            commentsSection.classList.add('show');
            expandIcon.classList.add('expanded');
            postItem.classList.add('expanded');

            // Only load comments if they're not already loaded (fallback for old posts)
            const container = document.getElementById(`comments-container-${postId}`);
            if (container.innerHTML.includes('Loading comments...')) {
                console.log(`📡 Loading comments on-demand for post ${postId} (fallback)`);
                await this.loadAndDisplayComments(postId);
            }
        }
    }

    async loadAndDisplayComments(postId) {
        const container = document.getElementById(`comments-container-${postId}`);
        const commentCountBadge = document.getElementById(`comment-count-${postId}`);
        const spamCountBadge = document.getElementById(`spam-count-${postId}`);

        try {
            console.log(`🔄 Loading comments for post ${postId}...`);
            const startTime = Date.now();

            // Show enhanced loading state with progress
            container.innerHTML = `
                <div class="text-center p-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 mb-1">Loading comments...</p>
                    <small class="text-muted" id="loading-status-${postId}">Fetching from Facebook...</small>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 30%"></div>
                    </div>
                </div>
            `;

            // Update loading status periodically
            this.updateLoadingStatus(postId);

            const comments = await this.loadCommentsForPost(postId);
            const loadTime = Date.now() - startTime;
            console.log(`⚡ Comments loaded in ${loadTime}ms`);

            if (comments.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted p-3">
                        <i class="fas fa-comment-slash fa-2x mb-2"></i>
                        <p class="mb-0">No comments found</p>
                    </div>
                `;
                commentCountBadge.innerHTML = '<i class="fas fa-comments me-1"></i>0 Comments';
                return;
            }

            // Progressive rendering - show comments as they're processed
            container.innerHTML = '<div class="comments-list"></div>';
            const commentsList = container.querySelector('.comments-list');

            // Render comments progressively
            comments.forEach((comment, index) => {
                setTimeout(() => {
                    const commentElement = document.createElement('div');
                    commentElement.innerHTML = this.renderComment(comment);
                    commentElement.style.opacity = '0';
                    commentElement.style.transform = 'translateY(10px)';

                    commentsList.appendChild(commentElement.firstElementChild);

                    // Animate in
                    requestAnimationFrame(() => {
                        commentElement.firstElementChild.style.transition = 'all 0.3s ease';
                        commentElement.firstElementChild.style.opacity = '1';
                        commentElement.firstElementChild.style.transform = 'translateY(0)';
                    });
                }, index * 50); // Stagger animation
            });

            // Update badges
            const spamCount = comments.filter(c => c.prediction?.is_spam).length;
            commentCountBadge.innerHTML = `<i class="fas fa-comments me-1"></i>${comments.length} Comments`;

            if (spamCount > 0) {
                spamCountBadge.innerHTML = `<i class="fas fa-exclamation-triangle me-1"></i>${spamCount} Spam`;
                spamCountBadge.style.display = 'inline-block';
            } else {
                spamCountBadge.style.display = 'none';
            }

        } catch (error) {
            console.error(`❌ Error loading comments:`, error);

            let errorMessage = error.message;
            let retryButton = '';

            if (error.message.includes('timeout')) {
                errorMessage = 'AI processing is taking longer than expected. This might be due to high server load.';
                retryButton = `
                    <button class="btn btn-sm btn-outline-primary mt-2"
                            onclick="dashboard.loadAndDisplayComments('${postId}')">
                        <i class="fas fa-redo me-1"></i>Try Again
                    </button>
                `;
            } else if (error.message.includes('AbortError')) {
                errorMessage = 'Request was cancelled. The AI model might be busy processing other requests.';
                retryButton = `
                    <button class="btn btn-sm btn-outline-primary mt-2"
                            onclick="dashboard.loadAndDisplayComments('${postId}')">
                        <i class="fas fa-redo me-1"></i>Retry
                    </button>
                `;
            }

            container.innerHTML = `
                <div class="alert alert-warning m-3">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Loading Failed</strong>
                    </div>
                    <p class="mb-2">${errorMessage}</p>
                    <small class="text-muted">
                        Tip: Try refreshing or check if the auto monitor is running (it might be using the AI model).
                    </small>
                    ${retryButton}
                </div>
            `;
            commentCountBadge.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Error';
        }
    }

    async refreshComments(postId) {
        const container = document.getElementById(`comments-container-${postId}`);
        container.innerHTML = `
            <div class="text-center p-3">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-1">Refreshing comments...</p>
                <small class="text-muted">Clearing cache and reloading...</small>
            </div>
        `;

        // Force refresh by adding query parameter
        try {
            const response = await fetch(`/api/posts/${postId}/comments?refresh=true`);
            if (response.ok) {
                const comments = await response.json();
                this.displayCommentsInContainer(postId, comments);
                this.showToast('Comments refreshed successfully!', 'success');
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            console.error('Error refreshing comments:', error);
            container.innerHTML = `
                <div class="alert alert-warning m-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Failed to refresh comments. Please try again.
                </div>
            `;
            this.showToast('Failed to refresh comments', 'error');
        }
    }

    updateLoadingStatus(postId) {
        const statusElement = document.getElementById(`loading-status-${postId}`);
        if (!statusElement) return;

        const messages = [
            'Fetching from Facebook...',
            'Processing with AI...',
            'Analyzing comments...',
            'Almost done...'
        ];

        let messageIndex = 0;
        const interval = setInterval(() => {
            if (!statusElement || !statusElement.isConnected) {
                clearInterval(interval);
                return;
            }

            messageIndex = (messageIndex + 1) % messages.length;
            statusElement.textContent = messages[messageIndex];

            // Update progress bar
            const progressBar = statusElement.parentElement.querySelector('.progress-bar');
            if (progressBar) {
                const progress = Math.min(30 + (messageIndex * 20), 90);
                progressBar.style.width = `${progress}%`;
            }
        }, 3000);

        // Clear interval after 30 seconds
        setTimeout(() => clearInterval(interval), 30000);
    }

    displayCommentsInContainer(postId, comments) {
        const container = document.getElementById(`comments-container-${postId}`);
        const commentCountBadge = document.getElementById(`comment-count-${postId}`);
        const spamCountBadge = document.getElementById(`spam-count-${postId}`);

        if (comments.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted p-3">
                    <i class="fas fa-comment-slash fa-2x mb-2"></i>
                    <p class="mb-0">No comments found</p>
                </div>
            `;
            commentCountBadge.innerHTML = '<i class="fas fa-comments me-1"></i>0 Comments';
            return;
        }

        // Render comments
        container.innerHTML = comments.map(comment => this.renderComment(comment)).join('');

        // Update badges
        const spamCount = comments.filter(c => c.prediction?.is_spam).length;
        const fallbackCount = comments.filter(c => c.prediction?.fallback).length;

        commentCountBadge.innerHTML = `<i class="fas fa-comments me-1"></i>${comments.length} Comments`;

        if (spamCount > 0) {
            spamCountBadge.innerHTML = `<i class="fas fa-exclamation-triangle me-1"></i>${spamCount} Spam`;
            spamCountBadge.style.display = 'inline-block';
        } else {
            spamCountBadge.style.display = 'none';
        }

        // Show warning if fallback was used
        if (fallbackCount > 0) {
            const warningBadge = document.createElement('span');
            warningBadge.className = 'badge bg-warning ms-2';
            warningBadge.innerHTML = `<i class="fas fa-exclamation-triangle me-1"></i>${fallbackCount} Fallback`;
            warningBadge.title = 'AI processing failed, using regex fallback';
            commentCountBadge.parentElement.appendChild(warningBadge);
        }
    }

    renderComment(comment) {
        const prediction = comment.prediction;
        const isSpam = prediction?.is_spam || false;
        const confidence = prediction?.confidence || 0;
        const commentDate = new Date(comment.created_time).toLocaleString();

        return `
            <div class="comment-item ${isSpam ? 'spam' : 'normal'}" data-comment-id="${comment.id}">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-1">
                            <strong class="me-2">${comment.from?.name || 'Unknown'}</strong>
                            <span class="badge bg-${isSpam ? 'danger' : 'success'} prediction-badge">
                                ${prediction?.label?.toUpperCase() || 'UNKNOWN'}
                            </span>
                            ${prediction?.fallback ? `
                                <span class="badge bg-warning ms-1" title="Regex fallback used">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </span>
                            ` : ''}
                            ${prediction?.method === 'regex_fallback' ? `
                                <small class="text-muted ms-2">(Regex)</small>
                            ` : ''}
                        </div>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            ${commentDate}
                        </small>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-sm ${isSpam ? 'btn-outline-danger' : 'btn-outline-secondary'} dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas ${isSpam ? 'fa-exclamation-triangle' : 'fa-ellipsis-v'}"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item text-danger" href="#" onclick="dashboard.deleteCommentManual('${comment.id}', '${comment.from?.name || 'Unknown'}')">
                                <i class="fas fa-trash me-2"></i>Delete Comment
                            </a></li>
                            ${isSpam ? `
                            <li><a class="dropdown-item" href="#" onclick="dashboard.markAsNotSpam('${comment.id}')">
                                <i class="fas fa-check me-2"></i>Mark as Not Spam
                            </a></li>
                            ` : ''}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-muted" href="#" onclick="dashboard.reportComment('${comment.id}')">
                                <i class="fas fa-flag me-2"></i>Report Comment
                            </a></li>
                        </ul>
                    </div>
                </div>
                <p class="mb-2 comment-message">${comment.message}</p>
                <div class="d-flex justify-content-between align-items-center">
                    <div class="confidence-bar flex-grow-1 me-3">
                        <div class="confidence-fill ${this.getConfidenceClass(confidence)}"
                             style="width: ${(confidence * 100).toFixed(1)}%"></div>
                    </div>
                    <small class="text-muted">
                        <i class="fas fa-brain me-1"></i>
                        ${(confidence * 100).toFixed(1)}%
                    </small>
                </div>
            </div>
        `;
    }

    async deleteComment(commentId) {
        if (!confirm('Are you sure you want to delete this comment?')) {
            return;
        }

        try {
            const response = await fetch(`/api/comments/${commentId}`, { method: 'DELETE' });
            const result = await response.json();

            if (result.success) {
                this.showToast('Comment deleted successfully!', 'success');

                // Remove comment from UI instead of full refresh
                const commentElement = document.querySelector(`[onclick*="${commentId}"]`)?.closest('.comment-item');
                if (commentElement) {
                    commentElement.style.animation = 'fadeOut 0.3s ease-out';
                    setTimeout(() => {
                        commentElement.remove();
                        this.updateCommentCounts();
                    }, 300);
                }
            } else {
                this.showToast(result.error || 'Failed to delete comment', 'error');
            }
        } catch (error) {
            this.showToast('Error deleting comment: ' + error.message, 'error');
        }
    }

    async deleteCommentManual(commentId, authorName) {
        // Show confirmation modal with reason selection
        const modal = this.createModerationModal(commentId, authorName);
        document.body.appendChild(modal);

        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        // Clean up modal when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    }

    createModerationModal(commentId, authorName) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-shield-alt me-2"></i>Manual Comment Moderation
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p class="mb-3">
                            <strong>Delete comment by:</strong> ${authorName}<br>
                            <strong>Comment ID:</strong> <code>${commentId}</code>
                        </p>
                        <div class="mb-3">
                            <label for="moderationReason" class="form-label">Reason for deletion:</label>
                            <select class="form-select" id="moderationReason">
                                <option value="Spam">Spam content</option>
                                <option value="Inappropriate">Inappropriate content</option>
                                <option value="Harassment">Harassment or bullying</option>
                                <option value="Misinformation">Misinformation</option>
                                <option value="Off-topic">Off-topic discussion</option>
                                <option value="Violation">Community guidelines violation</option>
                                <option value="Other">Other (specify below)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="moderatorName" class="form-label">Moderator name:</label>
                            <input type="text" class="form-control" id="moderatorName" value="Admin" placeholder="Enter moderator name">
                        </div>
                        <div class="mb-3">
                            <label for="additionalNotes" class="form-label">Additional notes (optional):</label>
                            <textarea class="form-control" id="additionalNotes" rows="2" placeholder="Any additional context..."></textarea>
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            This action cannot be undone. The comment will be permanently deleted from Facebook.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-danger" onclick="dashboard.confirmManualDeletion('${commentId}')">
                            <i class="fas fa-trash me-2"></i>Delete Comment
                        </button>
                    </div>
                </div>
            </div>
        `;
        return modal;
    }

    async confirmManualDeletion(commentId) {
        const reason = document.getElementById('moderationReason').value;
        const moderator = document.getElementById('moderatorName').value || 'Admin';
        const notes = document.getElementById('additionalNotes').value;

        // Close modal
        const modal = document.querySelector('.modal.show');
        if (modal) {
            bootstrap.Modal.getInstance(modal).hide();
        }

        try {
            // Find post ID for this comment
            const commentElement = this.findCommentElementById(commentId);
            const postElement = commentElement?.closest('[data-post-id]');
            const postId = postElement?.dataset.postId;

            const response = await fetch(`/api/comments/${commentId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    postId: postId,
                    reason: reason + (notes ? ` - ${notes}` : ''),
                    moderator: moderator
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showToast(`Comment deleted by ${moderator} - ${reason}`, 'success');
                // The real-time event will handle UI removal
            } else {
                this.showToast(result.error || 'Failed to delete comment', 'error');
            }
        } catch (error) {
            this.showToast('Error deleting comment: ' + error.message, 'error');
        }
    }

    async markAsNotSpam(commentId) {
        // This is a placeholder for future implementation
        // Could be used to retrain the model or add to whitelist
        this.showToast('Feature coming soon: Mark as not spam', 'info');
    }

    async reportComment(commentId) {
        // This is a placeholder for future implementation
        // Could be used to report to Facebook or internal system
        this.showToast('Feature coming soon: Report comment', 'info');
    }

    updateCommentCounts() {
        // Update comment counts after deletion
        document.querySelectorAll('[data-post-id]').forEach(postElement => {
            const postId = postElement.dataset.postId;
            const commentsContainer = document.getElementById(`comments-container-${postId}`);
            const commentCountBadge = document.getElementById(`comment-count-${postId}`);
            const spamCountBadge = document.getElementById(`spam-count-${postId}`);

            if (commentsContainer) {
                const totalComments = commentsContainer.querySelectorAll('.comment-item').length;
                const spamComments = commentsContainer.querySelectorAll('.comment-item.spam').length;

                commentCountBadge.innerHTML = `<i class="fas fa-comments me-1"></i>${totalComments} Comments`;

                if (spamComments > 0) {
                    spamCountBadge.innerHTML = `<i class="fas fa-exclamation-triangle me-1"></i>${spamComments} Spam`;
                    spamCountBadge.style.display = 'inline-block';
                } else {
                    spamCountBadge.style.display = 'none';
                }
            }
        });
    }

    startStatusUpdates() {
        this.statusUpdateInterval = setInterval(() => {
            this.updateStatus();
        }, 5000); // Update every 5 seconds
    }

    initRealTimeUpdates() {
        // Initialize Server-Sent Events for real-time updates
        if (typeof EventSource !== 'undefined') {
            console.log('🔄 Initializing real-time updates...');
            this.updateRealTimeIndicator('connecting', 'Connecting...');

            this.eventSource = new EventSource('/api/events');

            this.eventSource.onopen = () => {
                console.log('📡 Real-time connection established');
                this.updateRealTimeIndicator('connected', 'Live Updates');
                this.showToast('Real-time updates connected', 'success');
            };

            this.eventSource.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleRealTimeEvent(data);
                } catch (error) {
                    console.error('Error parsing SSE data:', error);
                }
            };

            this.eventSource.onerror = (error) => {
                console.error('SSE connection error:', error);
                this.updateRealTimeIndicator('disconnected', 'Disconnected');
                this.showToast('Real-time connection lost. Retrying...', 'warning');

                // Retry connection after 5 seconds
                setTimeout(() => {
                    if (this.eventSource.readyState === EventSource.CLOSED) {
                        this.updateRealTimeIndicator('connecting', 'Reconnecting...');
                        this.initRealTimeUpdates();
                    }
                }, 5000);
            };
        } else {
            console.warn('EventSource not supported by this browser');
            this.showToast('Real-time updates not supported in this browser', 'warning');
        }
    }

    updateRealTimeIndicator(status, text) {
        const indicator = document.getElementById('realtime-indicator');
        const statusText = document.getElementById('realtime-status');

        if (indicator && statusText) {
            indicator.style.display = 'block';
            indicator.className = `realtime-indicator ${status}`;
            statusText.textContent = text;

            // Auto-hide after 3 seconds if connected
            if (status === 'connected') {
                setTimeout(() => {
                    if (indicator.classList.contains('connected')) {
                        indicator.style.display = 'none';
                    }
                }, 3000);
            }
        }
    }

    handleRealTimeEvent(data) {
        // Only log non-heartbeat events to reduce console noise
        if (data.type !== 'heartbeat') {
            console.log('📨 Real-time event received:', data.type, data);
        }

        switch (data.type) {
            case 'connected':
                console.log('✅ Real-time updates ready');
                this.lastHeartbeat = Date.now();
                break;

            case 'heartbeat':
                // Update last heartbeat time
                this.lastHeartbeat = Date.now();
                break;

            case 'new_comment':
                this.handleNewComment(data);
                break;

            case 'spam_comment_removed':
                this.handleSpamCommentRemoved(data);
                break;

            case 'comment_deleted':
                this.handleCommentDeleted(data);
                break;

            case 'comment_edited':
                this.handleCommentEdited(data);
                break;

            case 'comment_deleted_external':
                this.handleCommentDeletedExternal(data);
                break;

            case 'comment_deleted_manual':
                this.handleCommentDeletedManual(data);
                break;

            default:
                console.log('Unknown event type:', data.type);
        }
    }

    handleNewComment(data) {
        const { comment, postId } = data;
        console.log(`💬 New comment on post ${postId}:`, comment.message.substring(0, 50));

        // Find the post container
        const postElement = document.querySelector(`[data-post-id="${postId}"]`);
        if (!postElement) {
            console.warn(`Post ${postId} not found in UI`);
            return;
        }

        // Get comments container
        const commentsContainer = document.getElementById(`comments-container-${postId}`);
        if (!commentsContainer) {
            console.warn(`Comments container for post ${postId} not found`);
            return;
        }

        // Check if comments section exists and has comments
        let commentsList = commentsContainer.querySelector('.comments-list');
        if (!commentsList) {
            // If no comments list exists, create one
            if (commentsContainer.innerHTML.includes('No comments found')) {
                commentsContainer.innerHTML = '<div class="comments-list"></div>';
                commentsList = commentsContainer.querySelector('.comments-list');
            } else {
                // Find existing comments and wrap them in comments-list if needed
                const existingComments = commentsContainer.querySelectorAll('.comment-item');
                if (existingComments.length > 0) {
                    commentsContainer.innerHTML = '<div class="comments-list"></div>';
                    commentsList = commentsContainer.querySelector('.comments-list');
                    existingComments.forEach(comment => commentsList.appendChild(comment));
                } else {
                    commentsContainer.innerHTML = '<div class="comments-list"></div>';
                    commentsList = commentsContainer.querySelector('.comments-list');
                }
            }
        }

        // Create new comment element
        const commentElement = document.createElement('div');
        commentElement.innerHTML = this.renderComment(comment);
        const commentDiv = commentElement.firstElementChild;

        // Add new comment indicator
        commentDiv.classList.add('new-comment');

        // Insert at the top of comments list
        commentsList.insertBefore(commentDiv, commentsList.firstChild);

        // Animate the new comment
        commentDiv.style.opacity = '0';
        commentDiv.style.transform = 'translateY(-20px)';
        commentDiv.style.transition = 'all 0.5s ease';

        requestAnimationFrame(() => {
            commentDiv.style.opacity = '1';
            commentDiv.style.transform = 'translateY(0)';
        });

        // Update comment counts
        this.updateCommentCountsForPost(postId);

        // Show notification
        const isSpam = comment.prediction?.is_spam;
        const notificationMessage = isSpam
            ? `New spam comment detected on post ${postId.split('_')[1]}`
            : `New comment on post ${postId.split('_')[1]} by ${comment.from?.name || 'Unknown'}`;

        this.showToast(notificationMessage, isSpam ? 'warning' : 'info');

        // Remove new comment indicator after 5 seconds
        setTimeout(() => {
            commentDiv.classList.remove('new-comment');
        }, 5000);
    }

    handleSpamCommentRemoved(data) {
        const { commentId, postId, message, author } = data;
        console.log(`🚨 Spam comment removed from post ${postId}:`, message);

        // Find and remove the spam comment from UI
        const commentElement = this.findCommentElementById(commentId);
        if (commentElement) {
            // Add spam removal animation
            commentElement.classList.add('spam-being-removed');
            commentElement.style.animation = 'spamRemovalSlide 1s ease-out forwards';

            setTimeout(() => {
                commentElement.remove();
                this.updateCommentCountsForPost(postId);
            }, 1000);

            console.log(`✅ Spam comment ${commentId} removed from UI`);
        } else {
            console.warn(`Spam comment ${commentId} not found in UI for removal`);
        }

        // Show notification
        this.showToast(`Spam comment by ${author} was automatically removed`, 'success');
    }

    handleCommentDeleted(data) {
        const { commentId } = data;
        console.log(`🗑️ Comment deleted:`, commentId);

        // Find and remove the comment from UI
        const commentElement = document.querySelector(`[onclick*="${commentId}"]`)?.closest('.comment-item');
        if (commentElement) {
            commentElement.style.animation = 'fadeOut 0.3s ease-out';
            setTimeout(() => {
                commentElement.remove();
                this.updateCommentCounts();
            }, 300);
        }

        this.showToast('Comment deleted successfully', 'success');
    }

    updateCommentCountsForPost(postId) {
        const postElement = document.querySelector(`[data-post-id="${postId}"]`);
        if (!postElement) return;

        const commentsContainer = document.getElementById(`comments-container-${postId}`);
        const commentCountBadge = document.getElementById(`comment-count-${postId}`);
        const spamCountBadge = document.getElementById(`spam-count-${postId}`);

        if (commentsContainer && commentCountBadge && spamCountBadge) {
            const totalComments = commentsContainer.querySelectorAll('.comment-item').length;
            const spamComments = commentsContainer.querySelectorAll('.comment-item.spam').length;

            commentCountBadge.innerHTML = `<i class="fas fa-comments me-1"></i>${totalComments} Comments`;

            if (spamComments > 0) {
                spamCountBadge.innerHTML = `<i class="fas fa-exclamation-triangle me-1"></i>${spamComments} Spam`;
                spamCountBadge.style.display = 'inline-block';
            } else {
                spamCountBadge.style.display = 'none';
            }
        }
    }

    handleCommentEdited(data) {
        const { comment, postId } = data;
        console.log(`✏️ Comment edited on post ${postId}:`, comment.newMessage?.substring(0, 50));

        // Find the comment element by ID
        const commentElement = this.findCommentElementById(comment.id);
        if (!commentElement) {
            console.warn(`Comment ${comment.id} not found in UI for edit`);
            return;
        }

        // Update comment content
        const messageElement = commentElement.querySelector('.comment-message');
        if (messageElement) {
            // Add edit indicator
            const oldMessage = messageElement.textContent;
            messageElement.innerHTML = `
                <div class="edited-comment">
                    <div class="new-message">${comment.newMessage || comment.message}</div>
                    <small class="text-muted edited-indicator">
                        <i class="fas fa-edit me-1"></i>Edited
                        <span class="edited-time">${new Date().toLocaleTimeString()}</span>
                    </small>
                </div>
            `;

            // Add edited styling
            commentElement.classList.add('comment-edited');

            // Animate the change
            messageElement.style.animation = 'editPulse 1s ease-in-out';

            // Show notification
            this.showToast(`Comment edited by ${comment.from?.name || 'Unknown'}`, 'info');

            // Remove animation after completion
            setTimeout(() => {
                messageElement.style.animation = '';
            }, 1000);
        }
    }

    handleCommentDeletedExternal(data) {
        const { commentId, postId, message, author } = data;
        console.log(`🗑️ Comment deleted externally: ${commentId} on post ${postId}`);

        // Find and remove the comment from UI
        const commentElement = this.findCommentElementById(commentId);
        if (commentElement) {
            // Add deletion animation
            commentElement.style.animation = 'fadeOutSlide 0.5s ease-out forwards';

            setTimeout(() => {
                commentElement.remove();
                this.updateCommentCountsForPost(postId);
            }, 500);

            // Show notification
            this.showToast(`Comment by ${author} was deleted`, 'warning');
        } else {
            console.warn(`Comment ${commentId} not found in UI for deletion`);
        }
    }

    handleCommentDeletedManual(data) {
        const { commentId, postId, reason, moderator } = data;
        console.log(`🛡️ Comment deleted by moderator: ${commentId} on post ${postId} (Reason: ${reason})`);

        // Find and remove the comment from UI
        const commentElement = this.findCommentElementById(commentId);
        if (commentElement) {
            // Add moderation deletion animation with special styling
            commentElement.classList.add('moderation-deleted');
            commentElement.style.animation = 'moderationRemoval 1.2s ease-out forwards';

            setTimeout(() => {
                commentElement.remove();
                this.updateCommentCountsForPost(postId);
            }, 1200);

            console.log(`✅ Comment ${commentId} removed from UI by moderation`);
        } else {
            console.warn(`Comment ${commentId} not found in UI for moderation removal`);
        }

        // Show notification with moderation context
        this.showToast(`Comment removed by ${moderator} - ${reason}`, 'warning');
    }

    findCommentElementById(commentId) {
        // Try different selectors to find the comment element
        let commentElement = document.querySelector(`[data-comment-id="${commentId}"]`);

        if (!commentElement) {
            // Try finding by onclick attribute (fallback)
            commentElement = document.querySelector(`[onclick*="${commentId}"]`)?.closest('.comment-item');
        }

        if (!commentElement) {
            // Try finding by any element containing the comment ID
            const allComments = document.querySelectorAll('.comment-item');
            for (const comment of allComments) {
                if (comment.innerHTML.includes(commentId)) {
                    commentElement = comment;
                    break;
                }
            }
        }

        return commentElement;
    }

    startHeartbeatMonitoring() {
        // Monitor heartbeat to detect connection issues
        this.lastHeartbeat = Date.now();

        setInterval(() => {
            const timeSinceLastHeartbeat = Date.now() - this.lastHeartbeat;
            const heartbeatTimeout = 60000; // 60 seconds

            if (timeSinceLastHeartbeat > heartbeatTimeout) {
                console.warn('⚠️ No heartbeat received, connection may be lost');
                this.updateRealTimeIndicator('disconnected', 'Connection Lost');

                // Try to reconnect
                if (this.eventSource && this.eventSource.readyState !== EventSource.CLOSED) {
                    this.eventSource.close();
                }

                setTimeout(() => {
                    console.log('🔄 Attempting to reconnect due to heartbeat timeout...');
                    this.initRealTimeUpdates();
                }, 2000);
            }
        }, 30000); // Check every 30 seconds
    }

    async updateStatus() {
        try {
            const response = await fetch('/api/status');
            const status = await response.json();
            
            // Update monitor status
            const monitorStatus = document.getElementById('monitor-status');
            const isRunning = status.monitor.isRunning;
            
            monitorStatus.textContent = isRunning ? 'Running' : 'Stopped';
            monitorStatus.className = `badge bg-${isRunning ? 'success' : 'secondary'}`;
            
            // Update statistics
            document.getElementById('comments-processed').textContent = status.monitor.commentsProcessed;
            document.getElementById('spam-removed').textContent = status.monitor.spamRemoved;
            
            // Update buttons
            this.updateMonitorButtons(isRunning);
        } catch (error) {
            console.error('Error updating status:', error);
        }
    }

    updateMonitorButtons(isRunning) {
        const startBtn = document.getElementById('start-monitor');
        const stopBtn = document.getElementById('stop-monitor');
        
        startBtn.disabled = isRunning;
        stopBtn.disabled = !isRunning;
    }

    getConfidenceClass(confidence) {
        if (confidence >= 0.8) return 'high';
        if (confidence >= 0.5) return 'medium';
        return 'low';
    }

    showLoading(buttonId) {
        const button = document.getElementById(buttonId);
        const originalText = button.innerHTML;
        button.dataset.originalText = originalText;
        button.innerHTML = '<span class="loading-spinner me-2"></span>Loading...';
        button.disabled = true;
    }

    hideLoading(buttonId) {
        const button = document.getElementById(buttonId);
        button.innerHTML = button.dataset.originalText;
        button.disabled = false;
    }

    togglePerformanceMonitor() {
        const monitor = document.getElementById('performance-monitor');
        const button = document.getElementById('toggle-performance');

        if (monitor.style.display === 'none') {
            monitor.style.display = 'block';
            button.classList.add('active');
            this.showToast('Performance monitor enabled', 'info');
        } else {
            monitor.style.display = 'none';
            button.classList.remove('active');
            this.showToast('Performance monitor disabled', 'info');
        }
    }

    updatePerformanceMonitor(metrics) {
        const monitor = document.getElementById('performance-monitor');
        if (monitor.style.display === 'none') return;

        // Update display values
        document.getElementById('api-time').textContent = metrics.apiTime ? `${metrics.apiTime}ms` : '-';
        document.getElementById('ai-time').textContent = metrics.aiTime ? `${metrics.aiTime}ms` : '-';
        document.getElementById('total-time').textContent = metrics.totalTime ? `${metrics.totalTime}ms` : '-';

        // Cache status with color coding
        const cacheElement = document.getElementById('cache-status');
        if (metrics.cached) {
            cacheElement.textContent = 'HIT';
            cacheElement.className = 'fw-bold text-success';
        } else {
            cacheElement.textContent = 'MISS';
            cacheElement.className = 'fw-bold text-warning';
        }

        // Color coding for total time performance
        const totalElement = document.getElementById('total-time');
        if (metrics.totalTime) {
            if (metrics.totalTime < 1000) {
                totalElement.className = 'fw-bold text-success';
            } else if (metrics.totalTime < 3000) {
                totalElement.className = 'fw-bold text-warning';
            } else {
                totalElement.className = 'fw-bold text-danger';
            }
        }

        // Color coding for AI time
        const aiElement = document.getElementById('ai-time');
        if (metrics.aiTime) {
            if (metrics.aiTime < 1000) {
                aiElement.className = 'fw-bold text-success';
            } else if (metrics.aiTime < 2000) {
                aiElement.className = 'fw-bold text-warning';
            } else {
                aiElement.className = 'fw-bold text-danger';
            }
        }

        console.log('📊 Performance metrics:', metrics);
    }

    showToast(message, type = 'info') {
        const toast = document.getElementById('toast');
        const toastBody = toast.querySelector('.toast-body');
        const toastHeader = toast.querySelector('.toast-header');

        // Set icon based on type
        const icons = {
            success: 'fas fa-check-circle text-success',
            error: 'fas fa-exclamation-circle text-danger',
            warning: 'fas fa-exclamation-triangle text-warning',
            info: 'fas fa-info-circle text-info'
        };

        toastHeader.querySelector('i').className = icons[type] || icons.info;
        toastBody.textContent = message;

        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new Dashboard();
});
