/* Dashboard Styles */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.status-card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s ease-in-out;
}

.status-card:hover {
    transform: translateY(-2px);
}

.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 10px;
    height: 100%;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0 !important;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
}

.btn-danger {
    background: linear-gradient(45deg, #dc3545, #fd7e14);
    border: none;
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #6610f2);
    border: none;
}

.post-item {
    border-left: 4px solid #007bff;
    background-color: #fff;
    margin-bottom: 1rem;
    border-radius: 0 8px 8px 0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.post-item:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.post-item.expanded {
    border-left-color: #28a745;
}

.post-item.comments-loaded {
    border-left-color: #007bff;
    border-left-width: 4px;
}

.post-item.comments-loaded .post-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.post-header {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.post-header:hover {
    background-color: #f8f9fa;
}

.post-content {
    padding: 0 1rem 1rem 1rem;
}

.expand-icon {
    transition: transform 0.3s ease;
}

.expand-icon.expanded {
    transform: rotate(180deg);
}

.comments-section {
    display: none;
    animation: slideDown 0.3s ease-out;
    border-top: 1px solid #e9ecef;
    background-color: #f8f9fa;
    margin: 0 -1rem -1rem -1rem;
    border-radius: 0 0 8px 8px;
}

.comments-section.show {
    display: block;
}

.comments-section.preloaded {
    background-color: #f0f8ff;
    border-top-color: #007bff;
}

.comments-section.preloaded .comments-header {
    background-color: #e3f2fd;
    border-bottom: 1px solid #bbdefb;
}

/* New comment animations and styling */
.comment-item.new-comment {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border-left: 4px solid #28a745;
    animation: newCommentPulse 2s ease-in-out;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.comment-item.new-comment::before {
    content: "NEW";
    position: absolute;
    top: 8px;
    right: 8px;
    background: #28a745;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
    animation: newBadgeFade 5s ease-in-out forwards;
}

@keyframes newCommentPulse {
    0% {
        background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
        transform: scale(1);
    }
    50% {
        background: linear-gradient(135deg, #d4edda 0%, #e8f5e8 100%);
        transform: scale(1.02);
    }
    100% {
        background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
        transform: scale(1);
    }
}

@keyframes newBadgeFade {
    0% { opacity: 1; }
    80% { opacity: 1; }
    100% { opacity: 0; }
}

/* Real-time connection indicator */
.realtime-indicator {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 1000;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.realtime-indicator.connected {
    background: #28a745;
    color: white;
}

.realtime-indicator.disconnected {
    background: #dc3545;
    color: white;
}

.realtime-indicator.connecting {
    background: #ffc107;
    color: #212529;
}

/* Comment edit and delete animations */
.comment-item.comment-edited {
    border-left: 4px solid #17a2b8;
    background: linear-gradient(135deg, #e8f4f8 0%, #f0f9fa 100%);
}

.edited-comment {
    position: relative;
}

.edited-indicator {
    display: block;
    margin-top: 5px;
    font-style: italic;
    color: #17a2b8 !important;
}

.edited-time {
    margin-left: 5px;
}

@keyframes editPulse {
    0% {
        background: linear-gradient(135deg, #e8f4f8 0%, #f0f9fa 100%);
        transform: scale(1);
    }
    50% {
        background: linear-gradient(135deg, #bee5eb 0%, #d1ecf1 100%);
        transform: scale(1.01);
    }
    100% {
        background: linear-gradient(135deg, #e8f4f8 0%, #f0f9fa 100%);
        transform: scale(1);
    }
}

@keyframes fadeOutSlide {
    0% {
        opacity: 1;
        transform: translateX(0);
        max-height: 200px;
    }
    50% {
        opacity: 0.5;
        transform: translateX(-20px);
    }
    100% {
        opacity: 0;
        transform: translateX(-50px);
        max-height: 0;
        padding: 0;
        margin: 0;
    }
}

/* Deleted comment placeholder */
.comment-deleted-placeholder {
    background: #f8d7da;
    border-left: 4px solid #dc3545;
    padding: 10px;
    margin: 5px 0;
    border-radius: 4px;
    font-style: italic;
    color: #721c24;
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Spam removal animation */
.comment-item.spam-being-removed {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-left: 4px solid #dc3545;
}

@keyframes spamRemovalSlide {
    0% {
        opacity: 1;
        transform: translateX(0) scale(1);
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    }
    25% {
        transform: translateX(-10px) scale(0.98);
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
    }
    50% {
        transform: translateX(10px) scale(0.95);
        opacity: 0.8;
    }
    75% {
        transform: translateX(-5px) scale(0.9);
        opacity: 0.5;
    }
    100% {
        opacity: 0;
        transform: translateX(-100px) scale(0.8);
        max-height: 0;
        padding: 0;
        margin: 0;
        border: none;
    }
}

/* Moderation removal animation */
.comment-item.moderation-deleted {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-left: 4px solid #ffc107;
}

@keyframes moderationRemoval {
    0% {
        opacity: 1;
        transform: translateX(0) scale(1);
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border-left: 4px solid #ffc107;
    }
    20% {
        background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);
        color: #212529;
        border-left: 4px solid #ff8c00;
    }
    40% {
        transform: translateX(-15px) scale(0.98);
        background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 100%);
        color: white;
    }
    60% {
        transform: translateX(15px) scale(0.95);
        opacity: 0.8;
    }
    80% {
        transform: translateX(-8px) scale(0.9);
        opacity: 0.4;
    }
    100% {
        opacity: 0;
        transform: translateX(-120px) scale(0.7);
        max-height: 0;
        padding: 0;
        margin: 0;
        border: none;
    }
}

.comments-header {
    padding: 1rem;
    background-color: #e9ecef;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.comments-container {
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.comment-item {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    border-left: 3px solid #6c757d;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.comment-item:hover {
    transform: translateX(2px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

.comment-item:last-child {
    margin-bottom: 0;
}

.comment-item.spam {
    border-left-color: #dc3545;
    background-color: #fff5f5;
}

.comment-item.normal {
    border-left-color: #28a745;
    background-color: #f8fff8;
}

.prediction-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.confidence-bar {
    height: 4px;
    background-color: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.confidence-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.confidence-fill.high {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.confidence-fill.medium {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.confidence-fill.low {
    background: linear-gradient(90deg, #6c757d, #adb5bd);
}

.test-result {
    border-radius: 8px;
    padding: 1rem;
}

.test-result.spam {
    background-color: #fff5f5;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.test-result.normal {
    background-color: #f8fff8;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 500px;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateX(0);
        max-height: 200px;
    }
    to {
        opacity: 0;
        transform: translateX(-20px);
        max-height: 0;
        padding: 0;
        margin: 0;
    }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Status indicators */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-indicator.running {
    background-color: #28a745;
    animation: pulse 2s infinite;
}

.status-indicator.stopped {
    background-color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .status-card {
        margin-bottom: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        font-size: 0.9rem;
    }

    .post-header {
        padding: 0.75rem;
    }

    .post-content {
        padding: 0 0.75rem 0.75rem 0.75rem;
    }

    .comments-container {
        max-height: 300px;
    }

    .comment-item {
        padding: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .confidence-bar {
        height: 3px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #fff;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #fff;
    }
    
    .card-header {
        background-color: #2d2d2d;
        border-bottom-color: #404040;
    }
    
    .comment-item {
        background-color: #404040;
    }
    
    .comment-item.spam {
        background-color: #4a2c2c;
    }
    
    .comment-item.normal {
        background-color: #2c4a2c;
    }
}
